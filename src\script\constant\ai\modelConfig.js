/**
 * AI模型配置文件
 * 支持不同AI模型的差异化配置，包括请求头、参数映射、流式支持等
 */

// 获取当前环境的授权令牌
const getAuthToken = (id) => {
    const AuthTokenMap = {
        dify: {
            dev: 'Bearer app-j6ewRPc3lcNeQkCfyKK1oAvu',
            prod: 'Bearer app-pHmHImH04NMI9IYeF64r2NUM'
        },
        dify1: {
            dev: 'Bearer app-gtW3ZNwnwiC1Y4ycO4RdW3Sk',
            prod: 'Bearer app-pHmHImH04NMI9IYeF64r2NUM'
        }
    };
    const hostname = window.location.hostname;
    const MapFunc = Object.keys(AI_MODELS).reduce((acc, modelId) => {
        if (hostname.startsWith('127') || hostname.startsWith('localhost') || hostname.startsWith('192')) {
            acc[modelId] = AuthTokenMap[modelId] && AuthTokenMap[modelId].dev;
        } else {
            acc[modelId] = AuthTokenMap[modelId] && AuthTokenMap[modelId].prod;
        }
        return acc;
    }, {});
    return MapFunc[id] || '';
};

/**
 * 参数映射器
 * 将通用参数格式转换为各模型特定的请求格式
 */
export const ParamMappers = {
    /**
     * Dify格式映射器
     * @param {Object} commonParams 通用参数
     * @returns {Object} Dify格式的参数
     */
    difyFormat: (commonParams) => {
        // 从通用参数中提取查询内容
        let query = '';
        if (commonParams.query) {
            query = commonParams.query;
        } else if (commonParams.messages && commonParams.messages.length > 0) {
            // 从消息历史中提取最后一条用户消息
            const lastMessage = commonParams.messages[commonParams.messages.length - 1];
            query = lastMessage.content || '';
        }

        // 构建 Dify 请求参数
        const difyParams = {
            inputs: {},
            query: query,
            response_mode: commonParams.stream ? 'streaming' : 'blocking',
            conversation_id: commonParams.conversationId || '',
            user: commonParams.user || 'abc-123',
            files: []
        };

        return difyParams;
    }
};

/**
 * AI模型配置
 */
export const AI_MODELS = {
    dify: {
        id: 'dify',
        name: 'Dify',
        description: '基于 Dify 平台的知识问答助手',
        provider: 'Dify',

        // 网络配置
        gateway: '/dify-service',
        endpoint: '/v1/chat-messages',
        isStreaming: true,

        // 请求头配置 - 使用函数返回动态请求头
        getHeaders: () => ({
            'Content-Type': 'application/json',
            'Authorization': getAuthToken('dify')
        }),

        // 参数映射函数
        paramMapper: ParamMappers.difyFormat,

        // 响应解析配置
        responsePaths: {
            streamDataPath: 'answer',
            completeDataPath: 'answer',
            errorPath: 'message'
        },

        // 流式响应标识符
        streamIdentifiers: {
            dataPrefix: 'data: ',
            doneSignal: '[DONE]',
            endEvent: 'message_end'
        },

        // 模型能力配置
        capabilities: {
            supportStreaming: true,
            supportCancel: true,
            maxTokens: 4096,
            defaultTemperature: 0.7
        }
    },
    dify1: {
        id: 'dify1',
        name: 'Dify1',
        description: '基于 Dify 平台的知识问答助手',
        provider: 'Dify1',

        // 网络配置
        gateway: '/dify-service-noport',
        endpoint: '/v1/chat-messages',
        isStreaming: true,

        // 请求头配置 - 使用函数返回动态请求头
        getHeaders: () => ({
            'Content-Type': 'application/json',
            'Authorization': getAuthToken('dify1')
        }),

        // 参数映射函数
        paramMapper: ParamMappers.difyFormat,

        // 响应解析配置
        responsePaths: {
            streamDataPath: 'answer',
            completeDataPath: 'answer',
            errorPath: 'message'
        },

        // 流式响应标识符
        streamIdentifiers: {
            dataPrefix: 'data: ',
            doneSignal: '[DONE]',
            endEvent: 'message_end'
        },

        // 模型能力配置
        capabilities: {
            supportStreaming: true,
            supportCancel: true,
            maxTokens: 4096,
            defaultTemperature: 0.7
        }
    }
};

// 默认模型ID
export const DEFAULT_MODEL = 'dify';

/**
 * 获取模型配置
 * @param {string} modelId 模型ID
 * @returns {Object} 模型配置对象
 */
export const getModelConfig = (modelId = DEFAULT_MODEL) => {
    return AI_MODELS[modelId] || AI_MODELS[DEFAULT_MODEL];
};

/**
 * 获取模型列表
 * @returns {Array} 模型配置数组
 */
export const getModelList = () => {
    return Object.values(AI_MODELS);
};

/**
 * 获取模型的请求头配置
 * @param {string} modelId 模型ID
 * @param {Object} customHeaders 自定义请求头
 * @returns {Object} 合并后的请求头
 */
export const getModelHeaders = (modelId, customHeaders = {}) => {
    const modelConfig = getModelConfig(modelId);

    let baseHeaders = {};
    if (typeof modelConfig.getHeaders === 'function') {
        baseHeaders = modelConfig.getHeaders();
    } else if (modelConfig.headers) {
        // 向后兼容：支持静态 headers 配置
        baseHeaders = modelConfig.headers;
    }

    return {
        ...baseHeaders,
        ...customHeaders
    };
};

/**
 * 获取模型的网关和端点配置
 * @param {string} modelId 模型ID
 * @returns {Object} 网关和端点配置
 */
export const getModelEndpoint = (modelId) => {
    const modelConfig = getModelConfig(modelId);
    return {
        gateway: modelConfig.gateway,
        endpoint: modelConfig.endpoint
    };
};

/**
 * 检查模型是否支持流式响应
 * @param {string} modelId 模型ID
 * @returns {boolean} 是否支持流式响应
 */
export const isModelStreamingSupported = (modelId) => {
    const modelConfig = getModelConfig(modelId);
    return modelConfig.capabilities && modelConfig.capabilities.supportStreaming !== false;
};

/**
 * 转换通用参数为模型特定格式
 * @param {string} modelId 模型ID
 * @param {Object} commonParams 通用参数
 * @returns {Object} 模型特定格式的参数
 */
export const transformParams = (modelId, commonParams) => {
    const modelConfig = getModelConfig(modelId);

    if (typeof modelConfig.paramMapper === 'function') {
        return modelConfig.paramMapper(commonParams);
    }

    // 默认返回原始参数
    return commonParams;
};

/**
 * 响应数据提取器
 */
export class ResponseExtractor {
    constructor(modelId = DEFAULT_MODEL) {
        this.config = getModelConfig(modelId);
    }

    /**
     * 提取流式响应内容
     * @param {Object} data 响应数据
     * @returns {string|null} 提取的内容
     */
    extractStreamContent(data) {
        try {
            const path = this.config.responsePaths.streamDataPath;
            return this.getValueByPath(data, path);
        } catch (error) {
            return null;
        }
    }

    /**
     * 提取完整响应内容
     * @param {Object} data 响应数据
     * @returns {string|null} 提取的内容
     */
    extractCompleteContent(data) {
        try {
            const path = this.config.responsePaths.completeDataPath;
            return this.getValueByPath(data, path);
        } catch (error) {
            return null;
        }
    }

    /**
     * 提取错误信息
     * @param {Object} data 响应数据
     * @returns {Object} 错误信息对象
     */
    extractError(data) {
        try {
            const errorPath = this.config.responsePaths.errorPath;
            const errorMessage = this.getValueByPath(data, errorPath) || '未知错误';

            return {
                message: errorMessage,
                type: 'api_error'
            };
        } catch (error) {
            return { message: '未知错误' };
        }
    }

    /**
     * 检查是否为流式结束标识
     * @param {string} data 数据字符串
     * @returns {boolean} 是否结束
     */
    isStreamDone(data) {
        const { doneSignal, endEvent } = this.config.streamIdentifiers;
        return data.includes(doneSignal) ||
            (data.includes('event') && data.includes(endEvent));
    }

    /**
     * 根据路径提取值
     * @param {Object} obj 源对象
     * @param {string} path 属性路径
     * @returns {*} 提取的值
     */
    getValueByPath(obj, path) {
        if (!path || !obj) return null;

        const keys = path.split('.');
        let result = obj;

        for (const key of keys) {
            if (result === null || result === undefined) return null;
            result = result[key];
        }

        return result;
    }
}

// 导出默认配置
export default {
    models: AI_MODELS,
    defaultModel: DEFAULT_MODEL,
    getModelConfig,
    getModelList,
    getModelHeaders,
    getModelEndpoint,
    isModelStreamingSupported,
    transformParams,
    ResponseExtractor
};
