/**
 * AI 调试助手
 * 用于记录和诊断 AI 相关的配置和请求
 */

export const AIDebugHelper = {
    /**
     * 记录模型配置
     */
    logModelConfig(modelId, config) {
        console.group(`🔧 AI模型配置 - ${modelId}`);
        console.log('模型ID:', modelId);
        console.log('Gateway:', config.gateway);
        console.log('Endpoint:', config.endpoint);
        console.log('Headers:', config.headers);
        console.log('完整配置:', config);
        console.groupEnd();
    },

    /**
     * 记录 API 请求
     */
    logAPIRequest(url, params, headers) {
        console.group('📤 AI API 请求');
        console.log('URL:', url);
        console.log('参数:', params);
        console.log('请求头:', headers);
        console.groupEnd();
    },

    /**
     * 记录模型切换
     */
    logModelSwitch(oldModel, newModel, config) {
        console.group('🔄 模型切换');
        console.log('旧模型:', oldModel);
        console.log('新模型:', newModel);
        console.log('新配置:', config);
        console.groupEnd();
    },

    /**
     * 记录错误
     */
    logError(context, error) {
        console.group(`❌ AI错误 - ${context}`);
        console.error('错误信息:', error.message);
        console.error('完整错误:', error);
        console.groupEnd();
    }
};

export default AIDebugHelper;
