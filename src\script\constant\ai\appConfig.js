/**
 * AI应用配置常量
 */

// API配置
export const API_CONFIG = {
    timeout: 300000, // 5分钟超时
    retry: {
        maxRetries: 3,
        retryDelay: 1000
    },
    streaming: {
        timeout: 300000, // 流式响应超时时间
        bufferSize: 1024 * 16 // 16KB缓冲区大小
    }
};

// 错误配置
export const ERROR_CONFIG = {
    errorMessages: {
        client: '客户端错误，请检查请求参数',
        timeout: '请求超时，请稍后重试',
        network: '网络错误，请检查网络连接',
        server: '服务器错误，请稍后重试',
        auth: '认证失败，请重新登录',
        unknown: '发生未知错误，请稍后重试',
        cancelled: '请求已取消'
    },
    errorCodes: {
        CLIENT_ERROR: 400,
        AUTH_ERROR: 401,
        FORBIDDEN: 403,
        NOT_FOUND: 404,
        SERVER_ERROR: 500,
        TIMEOUT: 999
    }
};

// 消息配置
export const MESSAGE_CONFIG = {
    maxMessageLength: 10000, // 单条消息最大长度
    maxHistoryMessages: 50, // 历史消息最大条数
    streamingChunkSize: 100, // 流式响应每次更新的最小字符数
    typingDelay: 50 // 打字效果延迟（毫秒）
};

// UI配置
export const UI_CONFIG = {
    scrollBehavior: 'smooth', // 滚动行为
    scrollDelay: 100, // 滚动延迟
    loadingText: '正在调用知识问答服务，请稍候…',
    stopButtonText: '停止生成',
    errorMessageDuration: 5000, // 错误消息显示时长
    successMessageDuration: 3000 // 成功消息显示时长
};

// 存储配置
export const STORAGE_CONFIG = {
    sessionKey: 'ai_chat_session',
    messagesKey: 'ai_chat_messages',
    configKey: 'ai_chat_config',
    maxStorageSize: 5 * 1024 * 1024, // 5MB
    autoSaveInterval: 30000 // 30秒自动保存
};

// 默认值配置
export const DEFAULT_CONFIG = {
    model: 'dify',
    temperature: 0.7,
    maxTokens: 4096,
    user: 'default-user',
    conversationId: ''
};

// 导出所有配置
export default {
    API_CONFIG,
    ERROR_CONFIG,
    MESSAGE_CONFIG,
    UI_CONFIG,
    STORAGE_CONFIG,
    DEFAULT_CONFIG
};
