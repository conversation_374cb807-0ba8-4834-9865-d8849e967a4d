import { aiConfigStreamPost } from '../ai-service-config';

// API 服务地址配置
const gateWayStream = '/dify-service';

export default {
    // ==================== 流式聊天接口 ====================

    // Dify 流式聊天接口
    difyStreamChatFlow(params, cancelToken = null, onData = null, onComplete = null, onError = null) {
        // 转换为 Dify 专用的参数格式
        let query = '';
        if (params.query) {
            query = params.query;
        } else if (params.messages && params.messages.length > 0) {
            query = params.messages[params.messages.length - 1].content;
        }

        const difyParams = {
            inputs: {},
            query: query,
            response_mode: 'streaming',
            conversation_id: '',
            user: 'abc-123',
            files: []
        };
        const difyHeaders = {
        };
        if (window.location.hostname.startsWith('127') || window.location.hostname.startsWith('localhost') || window.location.hostname.startsWith('192')) {
            difyHeaders.Authorization = 'Bearer app-j6ewRPc3lcNeQkCfyKK1oAvu';
        } else {
            difyHeaders.Authorization = 'Bearer app-pHmHImH04NMI9IYeF64r2NUM';
        }

        return aiConfigStreamPost({
            // url: '/work/flow/api/chat',
            url: '/v1/chat-messages',
            params: difyParams,
            headers: difyHeaders,
            gateWay: gateWayStream,
            cancelToken: cancelToken,
            onData: onData,
            onComplete: onComplete,
            onError: onError
        });
    }
};
