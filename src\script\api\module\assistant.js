import { aiConfigStreamPost } from '../ai-service-config';

export default {
    // ==================== 流式聊天接口 ====================

    /**
     * 通用流式聊天接口
     * @param {Object} params - 请求参数（已经转换好的格式）
     * @param {Object} config - 配置信息，包含 gateway、endpoint、headers 等
     * @param {Function} onData - 数据回调
     * @param {Function} onComplete - 完成回调
     * @param {Function} onError - 错误回调
     * @param {Object} cancelToken - 取消令牌
     */
    difyStreamChatFlow(params, config = {}, onData = null, onComplete = null, onError = null, cancelToken = null) {
        // 使用传入的配置，不再硬编码
        const {
            gateway = '/dify-service',
            endpoint = '/v1/chat-messages',
            headers = {}
        } = config;

        return aiConfigStreamPost({
            url: endpoint,
            params: params,
            headers: headers,
            gateWay: gateway,
            cancelToken: cancelToken,
            onData: onData,
            onComplete: onComplete,
            onError: onError
        });
    }
};
