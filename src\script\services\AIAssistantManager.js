/**
 * AI助手管理服务类
 * 提供统一的AI对话管理功能，包括消息发送、流式响应处理、请求取消等
 */
import assistantApi from '../api/module/assistant';
import {
    getModelConfig,
    getModelHeaders,
    getModelEndpoint,
    isModelStreamingSupported,
    transformParams,
    ResponseExtractor,
    DEFAULT_MODEL
} from '../constant/ai/modelConfig';
import { API_CONFIG, ERROR_CONFIG, DEFAULT_CONFIG } from '../constant/ai/appConfig';
import AIDebugHelper from '../utils/ai/debugHelper';

class AIAssistantManager {
    constructor(config = {}) {
        // 获取模型配置
        const modelId = config.model || DEFAULT_MODEL;
        const modelConfig = getModelConfig(modelId);
        const endpointConfig = getModelEndpoint(modelId);

        // 合并配置
        this.config = {
            // 基础配置
            model: modelId,
            apiEndpoint: endpointConfig.endpoint,
            gateway: endpointConfig.gateway,
            timeout: API_CONFIG.timeout,
            maxRetries: API_CONFIG.retry.maxRetries,
            retryDelay: API_CONFIG.retry.retryDelay,
            streamTimeout: API_CONFIG.streaming.timeout,

            // AI模型配置
            temperature: (modelConfig.capabilities && modelConfig.capabilities.defaultTemperature) || DEFAULT_CONFIG.temperature,
            defaultModel: modelId,
            isStreaming: modelConfig.isStreaming,

            // 自定义请求头
            customHeaders: config.headers || {},

            // 覆盖用户自定义配置
            ...config
        };

        // 初始化响应提取器
        this.responseExtractor = new ResponseExtractor(modelId);

        // 获取流式响应标识符配置
        this.streamIdentifiers = modelConfig.streamIdentifiers || {
            dataPrefix: 'data: ',
            doneSignal: '[DONE]',
            endEvent: 'message_end'
        };

        // 请求管理
        this.currentRequest = null;
        this.cancelToken = null;
        this.isProcessing = false;

        // 状态管理
        this.isStreaming = false;
        this.streamBuffer = '';

        // 回调管理
        this.callbacks = {
            onChunk: null,
            onComplete: null,
            onError: null
        };
    }

    /**
     * 获取当前的动态请求头
     * @returns {Object} 当前的请求头
     */
    getCurrentHeaders() {
        return getModelHeaders(this.config.model, this.config.customHeaders);
    }

    /**
     * 发送消息到AI服务
     * @param {Object} messageData - 消息数据
     * @param {string} messageData.content - 消息内容
     * @param {string} messageData.model - 使用的模型
     * @param {Array} messageData.messages - 历史消息
     * @param {string} messageData.conversationId - 对话ID
     * @param {string} messageData.user - 用户ID
     * @param {Object} options - 选项
     * @param {boolean} options.stream - 是否使用流式响应
     * @param {Function} options.onChunk - 流式响应回调
     * @param {Function} options.onComplete - 完成回调
     * @param {Function} options.onError - 错误回调
     * @returns {Promise}
     */
    async sendMessage(messageData, options = {}) {
        const {
            stream = true,
            onChunk = null,
            onComplete = null,
            onError = null
        } = options;

        // 检查是否有正在进行的请求
        if (this.isProcessing) {
            const error = new Error('已有请求正在处理中，请先停止当前请求');
            if (onError) onError(error);
            throw error;
        }

        try {
            this.isProcessing = true;
            this.callbacks = { onChunk, onComplete, onError };

            // 如果消息数据中指定了模型，且与当前配置不同，先更新配置
            if (messageData.model && messageData.model !== this.config.model) {
                this.updateConfig({ model: messageData.model });
            }
            
            // 构建通用参数
            const commonParams = {
                model: this.config.model, // 使用当前配置的模型
                messages: messageData.messages || [],
                temperature: messageData.temperature || this.config.temperature,
                stream: stream,
                query: messageData.content,
                conversationId: messageData.conversationId || DEFAULT_CONFIG.conversationId,
                user: messageData.user || DEFAULT_CONFIG.user,
                ...messageData
            };

            // 使用模型特定的参数转换
            const requestData = transformParams(this.config.model, commonParams);

            // 检查模型是否支持流式响应
            const supportsStreaming = isModelStreamingSupported(this.config.model);

            if (stream && supportsStreaming) {
                return await this.handleStreamRequest(requestData);
            }
            
            // 当前只支持流式响应
            throw new Error('当前只支持流式响应模式');

        } catch (error) {
            this.isProcessing = false;
            if (this.callbacks.onError) {
                this.callbacks.onError(error);
            }
            throw error;
        }
    }

    /**
     * 处理流式请求
     */
    async handleStreamRequest(requestData) {
        try {
            this.isStreaming = true;
            this.streamBuffer = '';

            // 创建取消令牌
            if (typeof axios !== 'undefined' && axios.CancelToken) {
                this.cancelToken = axios.CancelToken.source();
                console.log('创建了新的cancelToken:', this.cancelToken);
            }

            // 构建配置对象，包含 gateway、endpoint 和 headers
            const apiConfig = {
                gateway: this.config.gateway,
                endpoint: this.config.apiEndpoint,
                headers: this.getCurrentHeaders()
            };
            
            // 调试日志
            AIDebugHelper.logModelConfig(this.config.model, apiConfig);
            AIDebugHelper.logAPIRequest(
                `${apiConfig.gateway}${apiConfig.endpoint}`,
                requestData,
                apiConfig.headers
            );

            // 使用现有的 difyStreamChatFlow 接口
            const response = await assistantApi.difyStreamChatFlow(
                requestData,
                apiConfig,
                this.handleStreamData.bind(this),
                this.handleStreamComplete.bind(this),
                this.handleStreamError.bind(this),
                this.cancelToken && this.cancelToken.token
            );

            return response;

        } catch (error) {
            this.handleError(error);
            throw error;
        } finally {
            this.cleanup();
        }
    }

    /**
     * 处理流式数据
     * @param {Object} data 流式数据
     */
    handleStreamData(data) {
        try {
            // 提取内容
            let content = '';
            if (data && data.text) {
                content = data.text;
            } else if (data && data.answer) {
                content = data.answer;
            } else {
                content = this.responseExtractor.extractStreamContent(data) || '';
            }

            if (content && this.callbacks.onChunk) {
                this.streamBuffer += content;
                this.callbacks.onChunk(content, this.streamBuffer);
            }

            // 检查是否为结束标识
            if (data && typeof data === 'object') {
                const dataStr = JSON.stringify(data);
                if (this.responseExtractor.isStreamDone(dataStr)) {
                    this.handleStreamComplete();
                }
            }
        } catch (error) {
            console.error('处理流式数据失败:', error);
        }
    }

    /**
     * 流式响应完成处理
     */
    handleStreamComplete() {
        if (this.callbacks.onComplete && this.isStreaming) {
            this.callbacks.onComplete(this.streamBuffer);
        }
        this.cleanup();
    }

    /**
     * 流式响应错误处理
     * @param {Error} error 错误对象
     */
    handleStreamError(error) {
        const handledError = this.handleError(error);
        if (this.callbacks.onError) {
            this.callbacks.onError(handledError);
        }
        this.cleanup();
    }

    /**
     * 停止当前生成
     */
    stopGeneration() {
        console.log('停止生成请求, cancelToken:', this.cancelToken);
        
        if (this.cancelToken) {
            console.log('正在取消请求...');
            this.cancelToken.cancel('用户取消请求');
            this.cancelToken = null;
        } else {
            console.log('没有找到cancelToken，可能请求已经结束');
        }

        // 清理状态
        this.cleanup();

        // 触发完成回调（带有当前已生成的内容）
        if (this.callbacks.onComplete && this.streamBuffer) {
            this.callbacks.onComplete(this.streamBuffer);
        }
    }

    /**
     * 清理状态
     */
    cleanup() {
        this.isProcessing = false;
        this.isStreaming = false;
        this.cancelToken = null;
        this.currentRequest = null;
        // 不清理 streamBuffer，保留已生成的内容
    }

    /**
     * 检查是否正在处理请求
     * @returns {boolean}
     */
    isResponding() {
        return this.isProcessing;
    }

    /**
     * 检查是否正在流式响应
     * @returns {boolean}
     */
    isStreamResponse() {
        return this.isStreaming;
    }

    /**
     * 错误处理
     * @param {Error} error 错误对象
     * @returns {Error} 处理后的错误对象
     */
    handleError(error) {
        // 检查是否是取消错误
        if (error.isCancelled || error.message === '请求已取消') {
            return new Error(ERROR_CONFIG.errorMessages.cancelled);
        }

        // 检查常见错误类型
        const errorMap = {
            'ECONNABORTED': ERROR_CONFIG.errorMessages.timeout,
            'Network Error': ERROR_CONFIG.errorMessages.network,
            'timeout': ERROR_CONFIG.errorMessages.timeout
        };

        for (const [key, message] of Object.entries(errorMap)) {
            if (error.message && error.message.includes(key)) {
                return new Error(message);
            }
        }

        // HTTP状态码错误处理
        if (error.response) {
            const status = error.response.status;
            if (status >= 500) {
                return new Error(ERROR_CONFIG.errorMessages.server);
            } else if (status === 401) {
                return new Error(ERROR_CONFIG.errorMessages.auth);
            } else if (status >= 400) {
                return new Error(ERROR_CONFIG.errorMessages.client);
            }
        }

        // 返回原始错误或未知错误
        return error.message ? error : new Error(ERROR_CONFIG.errorMessages.unknown);
    }

    /**
     * 重置管理器状态
     */
    reset() {
        this.stopGeneration();
        this.streamBuffer = '';
        this.callbacks = {
            onChunk: null,
            onComplete: null,
            onError: null
        };
    }

    /**
     * 销毁服务实例
     */
    destroy() {
        this.stopGeneration();
        this.reset();
    }

    /**
     * 获取当前配置
     * @returns {Object} 当前配置
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * 更新配置
     * @param {Object} newConfig 新配置
     */
    updateConfig(newConfig) {
        // 如果传入了新的模型ID，重新加载完整的模型配置
        if (newConfig.model && newConfig.model !== this.config.model) {
            const oldModel = this.config.model;
            const modelId = newConfig.model;
            const modelConfig = getModelConfig(modelId);
            const endpointConfig = getModelEndpoint(modelId);
            
            // 更新所有模型相关配置
            this.config = {
                ...this.config,
                ...newConfig,
                model: modelId,
                apiEndpoint: endpointConfig.endpoint,
                gateway: endpointConfig.gateway,
                isStreaming: modelConfig.isStreaming,
                temperature: (modelConfig.capabilities && modelConfig.capabilities.defaultTemperature) || this.config.temperature
            };
            
            // 更新响应提取器
            this.responseExtractor = new ResponseExtractor(modelId);
            
            // 更新流式响应标识符
            this.streamIdentifiers = modelConfig.streamIdentifiers || {
                dataPrefix: 'data: ',
                doneSignal: '[DONE]',
                endEvent: 'message_end'
            };
            
            // 调试日志
            AIDebugHelper.logModelSwitch(oldModel, modelId, this.config);
        } else {
            // 普通配置更新
            this.config = {
                ...this.config,
                ...newConfig
            };
        }
    }
}

// 创建单例实例
let instance = null;

/**
 * 获取AI助手管理器实例
 * @param {Object} config 配置选项
 * @returns {AIAssistantManager} 管理器实例
 */
export function getAIAssistantManager(config) {
    if (!instance) {
        instance = new AIAssistantManager(config);
    } else if (config) {
        instance.updateConfig(config);
    }
    return instance;
}

// 导出类和单例获取函数
export default AIAssistantManager;
export { AIAssistantManager };
